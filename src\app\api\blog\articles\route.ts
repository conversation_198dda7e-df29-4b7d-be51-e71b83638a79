import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const featured = searchParams.get('featured') === 'true';
    const tag = searchParams.get('tag');

    // Build query for published articles only
    let query = supabase
      .from('blog_articles')
      .select(`
        id,
        title,
        slug,
        excerpt,
        featured_image,
        is_featured,
        views,
        likes,
        comments_count,
        read_time,
        tags,
        published_at,
        blog_categories (name, color, slug)
      `, { count: 'exact' })
      .eq('is_published', true);

    // Apply filters
    if (category) {
      query = query.eq('blog_categories.slug', category);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,excerpt.ilike.%${search}%`);
    }

    if (featured) {
      query = query.eq('is_featured', true);
    }

    if (tag) {
      query = query.contains('tags', [tag]);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data: articles, error, count } = await query
      .order('published_at', { ascending: false })
      .range(from, to);

    if (error) {
      console.error('Error fetching articles:', error);
      return NextResponse.json({ error: 'Failed to fetch articles' }, { status: 500 });
    }

    return NextResponse.json({
      articles: articles || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error in blog articles API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
